import 'dart:typed_data';
import 'lib/core/audio/multi_frame_voice_assembler.dart';

void main() {
  print('测试多帧MELP数据打包和解包...');
  
  // 创建4个模拟的MELP帧，每帧11字节
  final frame1 = Uint8List.fromList(List.generate(11, (i) => 0x01));
  final frame2 = Uint8List.fromList(List.generate(11, (i) => 0x02));
  final frame3 = Uint8List.fromList(List.generate(11, (i) => 0x03));
  final frame4 = Uint8List.fromList(List.generate(11, (i) => 0x04));
  
  final frames = [frame1, frame2, frame3, frame4];
  
  // 打包
  final assembler = MultiFrameVoiceAssembler.instance;
  final packedData = assembler.packFrames(frames);
  
  print('原始帧数: ${frames.length}');
  print('每帧大小: ${frames[0].length}字节');
  print('打包后大小: ${packedData.length}字节');
  print('预期大小: ${frames.length * 11}字节');
  
  // 验证打包后的大小
  if (packedData.length == frames.length * 11) {
    print('✅ 打包大小正确！');
  } else {
    print('❌ 打包大小错误！');
  }
  
  // 解包
  final unpackedFrames = assembler.unpackFrames(packedData);
  
  print('解包后帧数: ${unpackedFrames.length}');
  
  // 验证解包结果
  if (unpackedFrames.length == frames.length) {
    print('✅ 解包帧数正确！');
    
    bool allFramesMatch = true;
    for (int i = 0; i < frames.length; i++) {
      if (unpackedFrames[i].length != frames[i].length) {
        allFramesMatch = false;
        break;
      }
      for (int j = 0; j < frames[i].length; j++) {
        if (unpackedFrames[i][j] != frames[i][j]) {
          allFramesMatch = false;
          break;
        }
      }
    }
    
    if (allFramesMatch) {
      print('✅ 所有帧数据匹配！');
    } else {
      print('❌ 帧数据不匹配！');
    }
  } else {
    print('❌ 解包帧数错误！');
  }
  
  print('\n测试完成！');
}
