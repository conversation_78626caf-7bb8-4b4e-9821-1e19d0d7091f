import 'package:flutter_test/flutter_test.dart';
import '../lib/core/audio/voice_player.dart';
import '../lib/core/protocol/di_data_dispatcher.dart';

void main() {
  group('VoicePlayer预初始化测试', () {
    test('VoicePlayer预初始化方法存在', () async {
      // 测试VoicePlayer是否有预初始化方法
      final voicePlayer = await VoicePlayer.create();

      // 验证预初始化方法存在
      expect(voicePlayer.preInitializePcmPlayer, isA<Function>());

      // 验证重置方法存在
      expect(voicePlayer.resetPcmPlayer, isA<Function>());

      await voicePlayer.dispose();
    });

    test('DiDataDispatcher预初始化方法存在', () {
      // 测试DiDataDispatcher是否有预初始化方法
      final dispatcher = DiDataDispatcher.instance;

      // 验证预初始化方法存在
      expect(dispatcher.preInitializeVoicePlayer, isA<Function>());

      // 验证重置方法存在
      expect(dispatcher.resetVoicePlayer, isA<Function>());
    });
  });
}
